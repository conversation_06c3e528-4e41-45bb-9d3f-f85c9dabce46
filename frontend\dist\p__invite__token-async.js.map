{"version": 3, "sources": ["src/pages/invite/[token].tsx"], "sourcesContent": ["/**\n * 邀请链接处理页面\n * 路由: /invite/:token\n */\n\nimport { TeamOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';\nimport { Helmet, history, useParams } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Space,\n  Typography,\n  Result,\n  Spin,\n  Alert,\n  Divider,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { InvitationService } from '@/services';\nimport type { InvitationInfoResponse } from '@/types/api';\nimport { TokenManager } from '@/utils/tokenManager';\nimport Settings from '../../../config/defaultSettings';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    inviteCard: {\n      width: '100%',\n      maxWidth: 500,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n  };\n});\n\nconst InvitePage: React.FC = () => {\n  const { token } = useParams<{ token: string }>();\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState(false);\n  const [invitationInfo, setInvitationInfo] = useState<InvitationInfoResponse | null>(null);\n  const [result, setResult] = useState<any>(null);\n  const { styles } = useStyles();\n\n  // 获取邀请信息\n  const fetchInvitationInfo = async () => {\n    if (!token) {\n      setResult({\n        type: 'error',\n        title: '邀请链接无效',\n        message: '邀请链接格式错误或已过期',\n      });\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const response = await InvitationService.getInvitationInfo(token);\n\n      if (response.success) {\n        setInvitationInfo(response);\n      } else {\n        setResult({\n          type: 'error',\n          title: '邀请链接无效',\n          message: response.errorMessage || '无法获取邀请信息',\n        });\n      }\n    } catch (error) {\n      console.error('获取邀请信息失败:', error);\n      setResult({\n        type: 'error',\n        title: '获取邀请信息失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理邀请接受\n  const handleAcceptInvitation = async () => {\n    if (!token) return;\n\n    setProcessing(true);\n    try {\n      const response = await InvitationService.acceptInvitationByLink(token, {});\n\n      if (response.success) {\n        // 如果是新用户且返回了访问令牌，自动登录\n        if (response.isNewUser && response.accessToken) {\n          try {\n            TokenManager.setToken(response.accessToken);\n            console.log('新用户自动登录成功');\n\n            setResult({\n              type: 'success',\n              title: '欢迎加入团队！',\n              message: `您的账号已自动创建并登录。${response.nextAction || '正在跳转到仪表盘...'}`,\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: true,\n            });\n\n            // 延迟跳转到仪表盘\n            setTimeout(() => {\n              history.push('/dashboard');\n            }, 3000);\n          } catch (error) {\n            console.error('自动登录失败:', error);\n            setResult({\n              type: 'success',\n              title: '账号创建成功！',\n              message: '您的账号已成功创建，请使用邮箱验证码登录后查看团队信息。',\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: false,\n            });\n          }\n        } else if (response.isNewUser) {\n          // 新用户但没有自动登录令牌\n          setResult({\n            type: 'success',\n            title: '账号创建成功！',\n            message: '您的账号已成功创建，请使用邮箱验证码登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        } else {\n          // 现有用户\n          setResult({\n            type: 'success',\n            title: '加入成功！',\n            message: response.nextAction || '您已成功加入团队，请登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        }\n      } else {\n        setResult({\n          type: 'error',\n          title: '加入失败',\n          message: response.errorMessage || '处理邀请时发生错误',\n        });\n      }\n    } catch (error) {\n      console.error('处理邀请失败:', error);\n      setResult({\n        type: 'error',\n        title: '加入失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    history.push('/');\n  };\n\n  // 邀请确认界面\n  const InvitationConfirm = () => {\n    if (!invitationInfo) return null;\n\n    return (\n      <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n        <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />\n        <Title level={3}>团队邀请</Title>\n\n        <div style={{ marginBottom: 32 }}>\n          <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n            <div>\n              <Text type=\"secondary\">您被邀请加入团队：</Text>\n              <br />\n              <Title level={4} style={{ margin: '8px 0', color: '#1890ff' }}>\n                {invitationInfo.teamName}\n              </Title>\n            </div>\n\n            {invitationInfo.inviterName && (\n              <div>\n                <Text type=\"secondary\">邀请人：</Text>\n                <Text strong>{invitationInfo.inviterName}</Text>\n              </div>\n            )}\n\n            {invitationInfo.message && (\n              <div>\n                <Text type=\"secondary\">邀请消息：</Text>\n                <br />\n                <Text italic>\"{invitationInfo.message}\"</Text>\n              </div>\n            )}\n          </Space>\n        </div>\n\n        <Divider />\n\n        <div style={{ marginTop: 24 }}>\n          <Title level={4} style={{ marginBottom: 24 }}>\n            您确定要加入此团队吗？\n          </Title>\n\n          <Space size=\"large\">\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              loading={processing}\n              onClick={handleAcceptInvitation}\n              icon={<CheckCircleOutlined />}\n              disabled={invitationInfo.isExpired}\n            >\n              {processing ? '正在处理...' : '确认加入'}\n            </Button>\n            <Button\n              size=\"large\"\n              onClick={handleCancel}\n              icon={<CloseCircleOutlined />}\n              disabled={processing}\n            >\n              取消\n            </Button>\n          </Space>\n        </div>\n\n        {invitationInfo.isExpired && (\n          <Alert\n            message=\"邀请已过期\"\n            description=\"此邀请链接已过期，请联系团队管理员重新发送邀请。\"\n            type=\"warning\"\n            showIcon\n            style={{ marginTop: 24 }}\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 结果展示\n  const ResultDisplay = () => {\n    if (!result) return null;\n\n    // 根据结果类型和自动登录状态显示不同的按钮\n    const getExtraButtons = () => {\n      if (result.type === 'success') {\n        if (result.autoLogin) {\n          // 自动登录成功，显示跳转到仪表盘的按钮\n          return [\n            <Button type=\"primary\" key=\"dashboard\" onClick={() => history.push('/dashboard')}>\n              前往仪表盘\n            </Button>,\n          ];\n        } else if (result.isNewUser) {\n          // 新用户但未自动登录，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        } else {\n          // 现有用户，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        }\n      } else {\n        // 错误情况，显示重试和返回首页\n        return [\n          <Button type=\"primary\" key=\"retry\" onClick={() => window.location.reload()}>\n            重试\n          </Button>,\n          <Button key=\"home\" onClick={() => history.push('/')}>\n            返回首页\n          </Button>,\n        ];\n      }\n    };\n\n    return (\n      <Result\n        status={result.type}\n        title={result.title}\n        subTitle={result.message}\n        extra={getExtraButtons()}\n      />\n    );\n  };\n\n  // 页面加载时获取邀请信息\n  useEffect(() => {\n    fetchInvitationInfo();\n  }, [token]);\n\n  // 加载中状态\n  if (loading) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <div style={{ textAlign: 'center', padding: '60px 20px' }}>\n              <Spin size=\"large\" />\n              <div style={{ marginTop: 16 }}>\n                <Text type=\"secondary\">正在加载邀请信息...</Text>\n              </div>\n            </div>\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示结果页面\n  if (result) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <ResultDisplay />\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示邀请确认页面\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          团队邀请\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队邀请</Title>\n              <Text type=\"secondary\">加入团队，开始协作</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.inviteCard}>\n          <InvitationConfirm />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default InvitePage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BA8ZD;;;eAAA;;;;;;;8BA5ZuE;4BAC5B;6BAUpC;kCACsB;0DACc;mCACpB;iCACW;;;;;;mEAGb;;;;;;;;;;AAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAE7C,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBACE;YACF,gBAAgB;QAClB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,MAAM;YACJ,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,YAAY;YACV,OAAO;YACP,UAAU;YACV,WAAW,MAAM,iBAAiB;QACpC;QACA,QAAQ;YACN,WAAW;YACX,WAAW;QACb;IACF;AACF;AAEA,MAAM,aAAuB;;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,cAAS;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAgC;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAM;IAC1C,MAAM,EAAE,MAAM,EAAE,GAAG;IAGnB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,OAAO;YACV,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2BAAiB,CAAC,iBAAiB,CAAC;YAE3D,IAAI,SAAS,OAAO,EAClB,kBAAkB;iBAElB,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS,SAAS,YAAY,IAAI;YACpC;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAGA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,OAAO;QAEZ,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,2BAAiB,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAExE,IAAI,SAAS,OAAO,EAAE;gBAEpB,IAAI,SAAS,SAAS,IAAI,SAAS,WAAW,EAC5C,IAAI;oBACF,0BAAY,CAAC,QAAQ,CAAC,SAAS,WAAW;oBAC1C,QAAQ,GAAG,CAAC;oBAEZ,UAAU;wBACR,MAAM;wBACN,OAAO;wBACP,SAAS,CAAC,aAAa,EAAE,SAAS,UAAU,IAAI,cAAc,CAAC;wBAC/D,UAAU,SAAS,QAAQ;wBAC3B,WAAW,SAAS,SAAS;wBAC7B,WAAW;oBACb;oBAGA,WAAW;wBACT,YAAO,CAAC,IAAI,CAAC;oBACf,GAAG;gBACL,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,WAAW;oBACzB,UAAU;wBACR,MAAM;wBACN,OAAO;wBACP,SAAS;wBACT,UAAU,SAAS,QAAQ;wBAC3B,WAAW,SAAS,SAAS;wBAC7B,WAAW;oBACb;gBACF;qBACK,IAAI,SAAS,SAAS,EAE3B,UAAU;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,UAAU,SAAS,QAAQ;oBAC3B,WAAW,SAAS,SAAS;oBAC7B,WAAW;gBACb;qBAGA,UAAU;oBACR,MAAM;oBACN,OAAO;oBACP,SAAS,SAAS,UAAU,IAAI;oBAChC,UAAU,SAAS,QAAQ;oBAC3B,WAAW,SAAS,SAAS;oBAC7B,WAAW;gBACb;YAEJ,OACE,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS,SAAS,YAAY,IAAI;YACpC;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAGA,MAAM,eAAe;QACnB,YAAO,CAAC,IAAI,CAAC;IACf;IAGA,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB,OAAO;QAE5B,OACE,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAY;;gBACtD,2BAAC,mBAAY;oBAAC,OAAO;wBAAE,UAAU;wBAAI,OAAO;wBAAW,cAAc;oBAAG;;;;;;gBACxE,2BAAC;oBAAM,OAAO;8BAAG;;;;;;gBAEjB,2BAAC;oBAAI,OAAO;wBAAE,cAAc;oBAAG;8BAC7B,2BAAC,WAAK;wBAAC,WAAU;wBAAW,MAAK;wBAAS,OAAO;4BAAE,OAAO;wBAAO;;4BAC/D,2BAAC;;oCACC,2BAAC;wCAAK,MAAK;kDAAY;;;;;;oCACvB,2BAAC;;;;;oCACD,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,QAAQ;4CAAS,OAAO;wCAAU;kDACzD,eAAe,QAAQ;;;;;;;;;;;;4BAI3B,eAAe,WAAW,IACzB,2BAAC;;oCACC,2BAAC;wCAAK,MAAK;kDAAY;;;;;;oCACvB,2BAAC;wCAAK,MAAM;kDAAE,eAAe,WAAW;;;;;;;;;;;;4BAI3C,eAAe,OAAO,IACrB,2BAAC;;oCACC,2BAAC;wCAAK,MAAK;kDAAY;;;;;;oCACvB,2BAAC;;;;;oCACD,2BAAC;wCAAK,MAAM;;4CAAC;4CAAE,eAAe,OAAO;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;gBAM9C,2BAAC,aAAO;;;;;gBAER,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAG;;wBAC1B,2BAAC;4BAAM,OAAO;4BAAG,OAAO;gCAAE,cAAc;4BAAG;sCAAG;;;;;;wBAI9C,2BAAC,WAAK;4BAAC,MAAK;;gCACV,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAK;oCACL,SAAS;oCACT,SAAS;oCACT,MAAM,2BAAC,0BAAmB;;;;;oCAC1B,UAAU,eAAe,SAAS;8CAEjC,aAAa,YAAY;;;;;;gCAE5B,2BAAC,YAAM;oCACL,MAAK;oCACL,SAAS;oCACT,MAAM,2BAAC,0BAAmB;;;;;oCAC1B,UAAU;8CACX;;;;;;;;;;;;;;;;;;gBAMJ,eAAe,SAAS,IACvB,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,WAAW;oBAAG;;;;;;;;;;;;IAKjC;IAGA,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,OAAO;QAGpB,MAAM,kBAAkB;YACtB,IAAI,OAAO,IAAI,KAAK,WAAW;gBAC7B,IAAI,OAAO,SAAS,EAElB,OAAO;oBACL,2BAAC,YAAM;wBAAC,MAAK;wBAA0B,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAe;uBAAvD;;;;;iBAG5B;qBACI,IAAI,OAAO,SAAS,EAEzB,OAAO;oBACL,2BAAC,YAAM;wBAAC,MAAK;wBAAsB,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAgB;uBAApD;;;;;oBAG3B,2BAAC,YAAM;wBAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAM;uBAAzC;;;;;iBAGb;qBAGD,OAAO;oBACL,2BAAC,YAAM;wBAAC,MAAK;wBAAsB,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAgB;uBAApD;;;;;oBAG3B,2BAAC,YAAM;wBAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;kCAAM;uBAAzC;;;;;iBAGb;YAEL,OAEE,OAAO;gBACL,2BAAC,YAAM;oBAAC,MAAK;oBAAsB,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;8BAAI;mBAAjD;;;;;gBAG3B,2BAAC,YAAM;oBAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8BAAM;mBAAzC;;;;;aAGb;QAEL;QAEA,OACE,2BAAC,YAAM;YACL,QAAQ,OAAO,IAAI;YACnB,OAAO,OAAO,KAAK;YACnB,UAAU,OAAO,OAAO;YACxB,OAAO;;;;;;IAGb;IAGA,IAAA,gBAAS,EAAC;QACR;IACF,GAAG;QAAC;KAAM;IAGV,IAAI,SACF,OACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;YAC9B,2BAAC,WAAM;0BACL,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;YAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;0BAC5B,2BAAC,UAAI;oBAAC,WAAW,OAAO,UAAU;8BAChC,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAY;;4BACtD,2BAAC,UAAI;gCAAC,MAAK;;;;;;4BACX,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAG;0CAC1B,2BAAC;oCAAK,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;YAK/B,2BAAC,kBAAM;;;;;;;;;;;IAMb,IAAI,QACF,OACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;YAC9B,2BAAC,WAAM;0BACL,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;YAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;0BAC5B,2BAAC,UAAI;oBAAC,WAAW,OAAO,UAAU;8BAChC,2BAAC;;;;;;;;;;;;;;;YAGL,2BAAC,kBAAM;;;;;;;;;;;IAMb,OACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;YAC9B,2BAAC,WAAM;0BACL,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;YAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;;oBAC5B,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAM;4BAAS,MAAK;;gCAC9C,2BAAC;oCAAI,WAAW,OAAO,IAAI;8CACzB,2BAAC;wCAAI,KAAI;wCAAY,KAAI;wCAAW,QAAQ;;;;;;;;;;;gCAE9C,2BAAC;oCAAI,WAAW,OAAO,KAAK;;wCAC1B,2BAAC;4CAAM,OAAO;sDAAG;;;;;;wCACjB,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;oBAK7B,2BAAC,UAAI;wBAAC,WAAW,OAAO,UAAU;kCAChC,2BAAC;;;;;;;;;;oBAGH,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;YAG3B,2BAAC,kBAAM;;;;;;;;;;;AAGb;GA3VM;;QACc,cAAS;QAKR;;;KANf;IA6VN,WAAe"}