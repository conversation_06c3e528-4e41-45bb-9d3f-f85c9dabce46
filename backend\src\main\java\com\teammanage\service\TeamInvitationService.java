package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.teammanage.config.InvitationConfig;
import com.teammanage.dto.request.AcceptInvitationByLinkRequest;
import com.teammanage.dto.response.AcceptInvitationByLinkResponse;
import com.teammanage.dto.response.InvitationInfoResponse;
import com.teammanage.dto.response.SendInvitationResponse;
import com.teammanage.dto.response.TeamInvitationResponse;
import com.teammanage.entity.Account;
import com.teammanage.entity.Team;
import com.teammanage.entity.TeamInvitation;
import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.mapper.TeamInvitationMapper;
import com.teammanage.mapper.TeamMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.JwtTokenUtil;

/**
 * 团队邀请服务类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class TeamInvitationService {

    private static final Logger log = LoggerFactory.getLogger(TeamInvitationService.class);

    @Autowired
    private TokenCryptoService tokenCryptoService;

    /**
     * 用户处理结果
     */
    public static class UserProcessResult {
        private final Long userId;
        private final Boolean isNewUser;

        public UserProcessResult(Long userId, Boolean isNewUser) {
            this.userId = userId;
            this.isNewUser = isNewUser;
        }

        public Long getUserId() { return userId; }
        public Boolean getIsNewUser() { return isNewUser; }
    }

    @Autowired
    private TeamInvitationMapper teamInvitationMapper;

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Autowired
    private InvitationConfig invitationConfig;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserSessionService userSessionService;

    /**
     * 创建团队邀请
     * 
     * @param teamId 团队ID
     * @param inviterId 邀请人ID
     * @param emails 被邀请人邮箱列表
     * @param message 邀请消息
     * @return 创建的邀请列表
     */
    @Transactional
    public List<TeamInvitation> createInvitations(Long teamId, Long inviterId, List<String> emails, String message) {
        // 验证团队存在
        Team team = teamMapper.selectById(teamId);
        if (team == null || team.getIsDeleted()) {
            throw new ResourceNotFoundException("团队不存在");
        }

        // 验证邀请人存在
        Account inviter = accountMapper.selectById(inviterId);
        if (inviter == null) {
            throw new ResourceNotFoundException("邀请人不存在");
        }

        List<TeamInvitation> invitations = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiresAt = now.plusHours(invitationConfig.getExpireHours());

        for (String email : emails) {
            try {
                TeamInvitation invitation = createSingleInvitation(teamId, inviterId, email, message, now, expiresAt);
                invitations.add(invitation);
            } catch (Exception e) {
                log.warn("创建邀请失败: teamId={}, email={}, error={}", teamId, email, e.getMessage());
                // 继续处理其他邮箱，不中断整个流程
            }
        }

        log.info("批量创建邀请完成: teamId={}, inviterId={}, totalEmails={}, successCount={}", 
                teamId, inviterId, emails.size(), invitations.size());

        return invitations;
    }

    /**
     * 创建单个邀请
     */
    private TeamInvitation createSingleInvitation(Long teamId, Long inviterId, String email, 
                                                 String message, LocalDateTime now, LocalDateTime expiresAt) {
        // 检查邮箱格式
        if (!isValidEmail(email)) {
            throw new BusinessException("邮箱格式不正确: " + email);
        }

        // 查找被邀请人账户
        Account invitee = accountMapper.findByEmail(email);
        Long inviteeId = invitee != null ? invitee.getId() : null;

        // 检查是否已是团队成员
        if (inviteeId != null) {
            TeamMember existingMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, inviteeId);
            if (existingMember != null && !existingMember.getIsDeleted()) {
                throw new BusinessException("用户已是团队成员: " + email);
            }
        }

        // 检查是否已有待处理的邀请
        if (!invitationConfig.isAllowDuplicateInvitation()) {
            TeamInvitation existingInvitation = teamInvitationMapper.findPendingInvitation(teamId, email);
            if (existingInvitation != null) {
                throw new BusinessException("该邮箱已有待处理的邀请: " + email);
            }
        }

        // 创建邀请记录
        TeamInvitation invitation = new TeamInvitation();
        invitation.setTeamId(teamId);
        invitation.setInviterId(inviterId);
        invitation.setInviteeEmail(email);
        invitation.setInviteeId(inviteeId);
        invitation.setStatus(TeamInvitation.InvitationStatus.PENDING);
        invitation.setInvitedAt(now);
        invitation.setExpiresAt(expiresAt);
        invitation.setMessage(message);

        teamInvitationMapper.insert(invitation);

        log.info("邀请创建成功: invitationId={}, teamId={}, email={}", invitation.getId(), teamId, email);
        return invitation;
    }

    /**
     * 响应邀请
     * 
     * @param invitationId 邀请ID
     * @param userId 用户ID
     * @param accept 是否接受
     * @param responseMessage 响应消息
     */
    @Transactional
    public void respondToInvitation(Long invitationId, Long userId, boolean accept, String responseMessage) {
        // 获取邀请记录
        TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
        if (invitation == null) {
            throw new ResourceNotFoundException("邀请不存在");
        }

        // 验证用户权限
        Account user = accountMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在");
        }

        if (!user.getEmail().equals(invitation.getInviteeEmail())) {
            throw new BusinessException("无权限响应此邀请");
        }

        // 检查邀请状态
        if (!invitation.canBeResponded()) {
            throw new BusinessException("邀请无法响应，可能已过期或已处理");
        }

        // 更新邀请状态
        invitation.setInviteeId(userId);
        invitation.setStatus(accept ? TeamInvitation.InvitationStatus.ACCEPTED : TeamInvitation.InvitationStatus.REJECTED);
        invitation.setRespondedAt(LocalDateTime.now());
        if (responseMessage != null) {
            invitation.setMessage(invitation.getMessage() + "\n响应: " + responseMessage);
        }

        teamInvitationMapper.updateById(invitation);

        // 如果接受邀请，添加为团队成员
        if (accept) {
            addTeamMember(invitation.getTeamId(), userId);
        }

        log.info("邀请响应完成: invitationId={}, userId={}, accept={}", invitationId, userId, accept);
    }

    /**
     * 添加团队成员
     */
    private void addTeamMember(Long teamId, Long userId) {
        // 检查是否已是团队成员
        TeamMember existingMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        if (existingMember != null && !existingMember.getIsDeleted()) {
            log.warn("用户已是团队成员: teamId={}, userId={}", teamId, userId);
            return;
        }

        // 创建团队成员记录，使用新的角色系统
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(userId);
        // 使用新的角色系统，自动分配默认邀请角色
        member.setRole(TeamRole.getDefaultInvitationRole());
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(false); // 设置为停用状态，需要管理员激活
        member.setIsDeleted(false);

        teamMemberMapper.insert(member);
        log.info("团队成员添加成功(停用状态): teamId={}, userId={}, role={}",
                teamId, userId, member.getRole().getDisplayName());
    }

    /**
     * 取消邀请
     * 
     * @param invitationId 邀请ID
     * @param operatorId 操作人ID
     */
    @Transactional
    public void cancelInvitation(Long invitationId, Long operatorId) {
        TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
        if (invitation == null) {
            throw new ResourceNotFoundException("邀请不存在");
        }

        // 验证操作权限（只有邀请人可以取消）
        if (!invitation.getInviterId().equals(operatorId)) {
            throw new BusinessException("无权限取消此邀请");
        }

        // 检查邀请状态
        if (!invitation.canBeCancelled()) {
            throw new BusinessException("邀请无法取消，可能已被处理");
        }

        // 更新邀请状态
        invitation.setStatus(TeamInvitation.InvitationStatus.CANCELLED);
        invitation.setRespondedAt(LocalDateTime.now());

        teamInvitationMapper.updateById(invitation);

        log.info("邀请取消成功: invitationId={}, operatorId={}", invitationId, operatorId);
    }

    /**
     * 获取团队的邀请列表
     *
     * @param teamId 团队ID
     * @return 邀请列表
     */
    public List<TeamInvitationResponse> getTeamInvitations(Long teamId) {
        List<TeamInvitation> invitations = teamInvitationMapper.findByTeamId(teamId);
        return convertToResponseList(invitations);
    }

    /**
     * 获取用户收到的邀请列表
     *
     * @param email 用户邮箱
     * @return 邀请列表
     */
    public List<TeamInvitationResponse> getUserInvitations(String email) {
        List<TeamInvitation> invitations = teamInvitationMapper.findByInviteeEmail(email);
        return convertToResponseList(invitations);
    }

    /**
     * 获取用户收到的待处理邀请列表
     *
     * @param email 用户邮箱
     * @return 待处理邀请列表
     */
    public List<TeamInvitationResponse> getUserPendingInvitations(String email) {
        List<TeamInvitation> invitations = teamInvitationMapper.findByEmailAndStatus(email, "PENDING");
        return convertToResponseList(invitations).stream()
                .filter(inv -> !inv.getIsExpired())
                .toList();
    }

    /**
     * 更新过期邀请状态
     *
     * @return 更新数量
     */
    @Transactional
    public int updateExpiredInvitations() {
        int count = teamInvitationMapper.updateExpiredInvitations(LocalDateTime.now());
        if (count > 0) {
            log.info("更新过期邀请状态完成: count={}", count);
        }
        return count;
    }

    /**
     * 转换为响应DTO列表
     */
    private List<TeamInvitationResponse> convertToResponseList(List<TeamInvitation> invitations) {
        return invitations.stream()
                .map(this::convertToResponse)
                .toList();
    }

    /**
     * 转换为响应DTO
     */
    private TeamInvitationResponse convertToResponse(TeamInvitation invitation) {
        TeamInvitationResponse response = new TeamInvitationResponse();
        response.setId(invitation.getId());
        response.setTeamId(invitation.getTeamId());
        response.setInviterId(invitation.getInviterId());
        response.setInviteeEmail(invitation.getInviteeEmail());
        response.setInviteeId(invitation.getInviteeId());
        response.setStatus(invitation.getStatus());
        response.setInvitedAt(invitation.getInvitedAt());
        response.setRespondedAt(invitation.getRespondedAt());
        response.setExpiresAt(invitation.getExpiresAt());
        response.setMessage(invitation.getMessage());
        response.setCreatedAt(invitation.getCreatedAt());
        response.setUpdatedAt(invitation.getUpdatedAt());
        response.setIsExpired(invitation.isExpired());
        response.setCanBeResponded(invitation.canBeResponded());
        response.setCanBeCancelled(invitation.canBeCancelled());

        // 填充团队信息
        Team team = teamMapper.selectById(invitation.getTeamId());
        if (team != null) {
            response.setTeamName(team.getName());
        }

        // 填充邀请人信息
        Account inviter = accountMapper.selectById(invitation.getInviterId());
        if (inviter != null) {
            response.setInviterName(inviter.getName());
            response.setInviterEmail(inviter.getEmail());
        }

        // 填充被邀请人信息
        if (invitation.getInviteeId() != null) {
            Account invitee = accountMapper.selectById(invitation.getInviteeId());
            if (invitee != null) {
                response.setInviteeName(invitee.getName());
            }
        }

        return response;
    }

    /**
     * 简单的邮箱格式验证
     */
    private boolean isValidEmail(String email) {
        return email != null && email.contains("@") && email.contains(".");
    }

    /**
     * 创建带链接的团队邀请
     *
     * @param teamId 团队ID
     * @param inviterId 邀请人ID
     * @param emails 被邀请人邮箱列表
     * @param message 邀请消息
     * @return 发送邀请响应DTO
     */
    @Transactional
    public SendInvitationResponse createInvitationsWithLinks(Long teamId, Long inviterId, List<String> emails, String message) {
        // 创建邀请记录
        List<TeamInvitation> invitations = createInvitations(teamId, inviterId, emails, message);

        // 构建响应DTO
        SendInvitationResponse response = new SendInvitationResponse();
        response.setTotalCount(emails.size());
        response.setSuccessCount(invitations.size());
        response.setFailureCount(emails.size() - invitations.size());

        // 构建邀请结果列表
        List<SendInvitationResponse.InvitationResult> results = new ArrayList<>();

        // 为每个邀请生成链接
        for (TeamInvitation invitation : invitations) {
            SendInvitationResponse.InvitationResult result = new SendInvitationResponse.InvitationResult();
            result.setInvitationId(invitation.getId());
            result.setEmail(invitation.getInviteeEmail());
            result.setSuccess(true);

            // 生成邀请链接
            String invitationLink = generateInvitationLink(invitation.getId());
            System.err.println("invitationLink: " + invitationLink);
            result.setInvitationLink(invitationLink);

            results.add(result);
        }

        // 添加失败的邀请结果
        if (response.getFailureCount() > 0) {
            for (String email : emails) {
                // 检查是否已经在成功列表中
                boolean found = false;
                for (SendInvitationResponse.InvitationResult result : results) {
                    if (email.equals(result.getEmail())) {
                        found = true;
                        break;
                    }
                }

                // 如果不在成功列表中，添加失败结果
                if (!found) {
                    SendInvitationResponse.InvitationResult result = new SendInvitationResponse.InvitationResult();
                    result.setEmail(email);
                    result.setSuccess(false);
                    result.setErrorMessage("邀请创建失败");
                    results.add(result);
                }
            }
        }
        System.out.println("results: " + results);
        response.setInvitations(results);
        return response;
    }

    /**
     * 生成邀请链接
     *
     * @param invitationId 邀请ID
     * @return 邀请链接
     */
    public String generateInvitationLink(Long invitationId) {
        // 生成邀请令牌（简单加密invitationId）
        String invitationToken = generateInvitationToken(invitationId);

        // 构建邀请链接
        return invitationConfig.getBaseUrl() + "/invite/" + invitationToken;
    }

    /**
     * 生成邀请令牌
     *
     * @param invitationId 邀请ID
     * @return 邀请令牌
     */
    private String generateInvitationToken(Long invitationId) {
        // 获取邀请记录以确定过期时间
        TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
        if (invitation == null) {
            throw new IllegalArgumentException("邀请记录不存在: " + invitationId);
        }

        // 使用新的安全令牌服务生成加密令牌
        return tokenCryptoService.generateToken(invitationId, invitation.getExpiresAt());
    }

    /**
     * 从令牌中解析邀请ID（安全版本）
     *
     * @param token 邀请令牌
     * @return 邀请ID
     */
    private Long parseInvitationToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("邀请令牌为空");
            return null;
        }

        try {
            // 使用新的安全令牌服务解析令牌
            TokenCryptoService.TokenData tokenData = tokenCryptoService.parseToken(token);
            if (tokenData == null) {
                log.warn("令牌解析失败或已过期");
                return null;
            }

            return tokenData.getInvitationId();
        } catch (Exception e) {
            log.warn("解析邀请令牌失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理邀请链接
     *
     * @param invitationToken 邀请令牌
     * @param request 请求DTO
     * @return 处理结果
     */
    @Transactional
    public AcceptInvitationByLinkResponse processInvitationByToken(String invitationToken, AcceptInvitationByLinkRequest request) {
        AcceptInvitationByLinkResponse response = new AcceptInvitationByLinkResponse();

        try {
            // 解析邀请ID
            Long invitationId = parseInvitationToken(invitationToken);
            if (invitationId == null) {
                response.setSuccess(false);
                response.setErrorMessage("无效的邀请链接");
                return response;
            }

            // 获取邀请记录
            TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
            if (invitation == null) {
                response.setSuccess(false);
                response.setErrorMessage("邀请不存在");
                return response;
            }

            // 检查邀请状态
            if (!invitation.canBeResponded()) {
                response.setSuccess(false);
                response.setErrorMessage("邀请无法响应，可能已过期或已处理");
                return response;
            }

            // 获取团队信息
            Team team = teamMapper.selectById(invitation.getTeamId());
            if (team == null || team.getIsDeleted()) {
                response.setSuccess(false);
                response.setErrorMessage("团队不存在");
                return response;
            }

            // 设置团队信息
            response.setTeamId(team.getId());
            response.setTeamName(team.getName());

            // 处理用户信息
            UserProcessResult userResult;
            try {
                userResult = processUserForInvitation(invitation, request);
                if (userResult == null || userResult.getUserId() == null) {
                    response.setSuccess(false);
                    response.setErrorMessage("处理用户信息失败");
                    return response;
                }
            } catch (BusinessException e) {
                response.setSuccess(false);
                response.setErrorMessage(e.getMessage());
                return response;
            }

            // 更新邀请状态
            invitation.setInviteeId(userResult.getUserId());
            invitation.setStatus(TeamInvitation.InvitationStatus.ACCEPTED);
            invitation.setRespondedAt(LocalDateTime.now());
            if (request.getMessage() != null) {
                invitation.setMessage(invitation.getMessage() + "\n响应: " + request.getMessage());
            }

            teamInvitationMapper.updateById(invitation);

            // 添加为团队成员（设置isActive=false）
            addTeamMemberInactive(invitation.getTeamId(), userResult.getUserId());

            // 为新用户生成访问令牌，实现自动登录
            String accessToken = null;
            if (userResult.getIsNewUser()) {
                try {
                    Account account = accountMapper.selectById(userResult.getUserId());
                    if (account != null) {
                        accessToken = generateAccessTokenForUser(account);
                        log.info("为新用户生成访问令牌: userId={}, email={}", account.getId(), account.getEmail());
                    }
                } catch (Exception e) {
                    log.warn("生成访问令牌失败，用户需要手动登录: userId={}, error={}", userResult.getUserId(), e.getMessage());
                }
            }

            // 设置响应信息
            response.setSuccess(true);
            response.setUserId(userResult.getUserId());
            response.setIsNewUser(userResult.getIsNewUser());
            response.setAccessToken(accessToken);

            if (userResult.getIsNewUser() && accessToken != null) {
                response.setNextAction("账号已自动创建并登录，请等待团队管理员激活您的账号");
            } else {
                response.setNextAction("请等待团队管理员激活您的账号");
            }

            return response;
        } catch (Exception e) {
            log.error("处理邀请链接失败: {}", e.getMessage());
            response.setSuccess(false);
            response.setErrorMessage("处理邀请失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 处理邀请用户信息
     *
     * @param invitation 邀请记录
     * @param request 请求DTO
     * @return 用户处理结果
     */
    private UserProcessResult processUserForInvitation(TeamInvitation invitation, AcceptInvitationByLinkRequest request) {
        // 检查邀请邮箱
        String inviteeEmail = invitation.getInviteeEmail();

        // 查找用户
        Account account = accountMapper.findByEmail(inviteeEmail);

        // 如果用户存在，直接返回用户ID
        if (account != null) {
            return new UserProcessResult(account.getId(), false);
        }

        // 如果用户不存在，自动创建新用户
        Account newAccount = createNewUserFromInvitation(inviteeEmail, request);
        return new UserProcessResult(newAccount.getId(), true);
    }

    /**
     * 从邀请信息自动创建新用户
     *
     * @param email 邮箱地址
     * @param request 请求DTO（可能包含用户提供的姓名）
     * @return 创建的用户账号
     */
    private Account createNewUserFromInvitation(String email, AcceptInvitationByLinkRequest request) {
        // 确定用户姓名：优先使用请求中的姓名，否则从邮箱提取
        String userName;
        if (request.getName() != null && !request.getName().trim().isEmpty()) {
            userName = request.getName().trim();
        } else {
            // 从邮箱提取用户名作为默认姓名
            userName = email.substring(0, email.indexOf('@'));
        }

        // 创建新用户账户
        Account newAccount = new Account();
        newAccount.setEmail(email);
        newAccount.setName(userName);
        newAccount.setPasswordHash(null); // 邀请用户无需密码，使用验证码登录
        newAccount.setDefaultSubscriptionPlanId(1L); // 默认免费套餐
        newAccount.setCreatedAt(LocalDateTime.now());
        newAccount.setUpdatedAt(LocalDateTime.now());

        accountMapper.insert(newAccount);

        log.info("通过邀请自动创建新用户: email={}, name={}, id={}",
                email, userName, newAccount.getId());

        return newAccount;
    }

    /**
     * 为用户生成访问令牌
     *
     * @param account 用户账号
     * @return 访问令牌
     */
    private String generateAccessTokenForUser(Account account) {
        try {
            // 生成用户Token
            String userToken = jwtTokenUtil.generateToken(account);
            String jti = jwtTokenUtil.getJtiFromToken(userToken);

            // 创建会话记录
            com.teammanage.model.SessionInfo sessionInfo = com.teammanage.model.SessionInfo.builder()
                    .accountId(account.getId())
                    .tokenHash(jti)
                    .deviceInfo("邀请自动登录")
                    .ipAddress("unknown")
                    .userAgent("邀请链接")
                    .loginTime(LocalDateTime.now())
                    .lastActivityTime(LocalDateTime.now())
                    .isActive(true)
                    .build();

            userSessionService.createSession(sessionInfo);

            log.info("为邀请用户生成访问令牌成功: userId={}, email={}", account.getId(), account.getEmail());
            return userToken;
        } catch (Exception e) {
            log.error("生成访问令牌失败: userId={}, error={}", account.getId(), e.getMessage());
            throw new RuntimeException("生成访问令牌失败", e);
        }
    }

    /**
     * 添加团队成员（设置为停用状态）
     */
    private void addTeamMemberInactive(Long teamId, Long userId) {
        // 检查是否已是团队成员
        TeamMember existingMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        if (existingMember != null && !existingMember.getIsDeleted()) {
            log.warn("用户已是团队成员: teamId={}, userId={}", teamId, userId);
            return;
        }

        // 创建团队成员记录，使用新的角色系统
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(userId);
        // 使用新的角色系统，自动分配默认邀请角色
        member.setRole(TeamRole.getDefaultInvitationRole());
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(false); // 设置为停用状态
        member.setIsDeleted(false);

        teamMemberMapper.insert(member);
        log.info("团队成员添加成功(停用状态): teamId={}, userId={}, role={}",
                teamId, userId, member.getRole().getDisplayName());
    }

    /**
     * 获取邀请信息（通过令牌）
     *
     * @param invitationToken 邀请令牌
     * @return 邀请信息
     */
    public InvitationInfoResponse getInvitationInfo(String invitationToken) {
        InvitationInfoResponse response = new InvitationInfoResponse();

        try {
            // 解析邀请ID
            Long invitationId = parseInvitationToken(invitationToken);
            if (invitationId == null) {
                response.setSuccess(false);
                response.setErrorMessage("无效的邀请链接");
                return response;
            }

            // 获取邀请记录
            TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
            if (invitation == null) {
                response.setSuccess(false);
                response.setErrorMessage("邀请不存在");
                return response;
            }

            // 获取团队信息
            Team team = teamMapper.selectById(invitation.getTeamId());
            if (team == null || team.getIsDeleted()) {
                response.setSuccess(false);
                response.setErrorMessage("团队不存在");
                return response;
            }

            // 获取邀请人信息
            Account inviter = accountMapper.selectById(invitation.getInviterId());

            // 设置响应信息
            response.setSuccess(true);
            response.setTeamId(team.getId());
            response.setTeamName(team.getName());
            response.setInviterName(inviter != null ? inviter.getName() : "未知用户");
            response.setMessage(invitation.getMessage());
            response.setInvitedAt(invitation.getInvitedAt());
            response.setExpiresAt(invitation.getExpiresAt());
            response.setIsExpired(invitation.isExpired());
            response.setCanBeResponded(invitation.canBeResponded());

            return response;
        } catch (Exception e) {
            log.error("获取邀请信息失败: {}", e.getMessage());
            response.setSuccess(false);
            response.setErrorMessage("获取邀请信息失败: " + e.getMessage());
            return response;
        }
    }
}
