{"version": 3, "sources": ["p__invite__token-async.17476496896951426462.hot-update.js", "src/pages/invite/[token].tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'p__invite__token',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='5747851525162893863';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"],\"src/pages/user/team-select/index.tsx\":[\"common\",\"src/pages/user/team-select/index.tsx\"]});;\r\n  },\r\n);\r\n", "/**\n * 邀请链接处理页面\n * 路由: /invite/:token\n */\n\nimport { TeamOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';\nimport { Helmet, history, useParams } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Space,\n  Typography,\n  Result,\n  Spin,\n  Alert,\n  Divider,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { InvitationService } from '@/services';\nimport type { InvitationInfoResponse } from '@/types/api';\nimport { TokenManager } from '@/utils/tokenManager';\nimport Settings from '../../../config/defaultSettings';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    inviteCard: {\n      width: '100%',\n      maxWidth: 500,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n  };\n});\n\nconst InvitePage: React.FC = () => {\n  const { token } = useParams<{ token: string }>();\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState(false);\n  const [invitationInfo, setInvitationInfo] = useState<InvitationInfoResponse | null>(null);\n  const [result, setResult] = useState<any>(null);\n  const { styles } = useStyles();\n\n  // 获取邀请信息\n  const fetchInvitationInfo = async () => {\n    if (!token) {\n      setResult({\n        type: 'error',\n        title: '邀请链接无效',\n        message: '邀请链接格式错误或已过期',\n      });\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const response = await InvitationService.getInvitationInfo(token);\n\n      if (response.success) {\n        setInvitationInfo(response);\n      } else {\n        setResult({\n          type: 'error',\n          title: '邀请链接无效',\n          message: response.errorMessage || '无法获取邀请信息',\n        });\n      }\n    } catch (error) {\n      console.error('获取邀请信息失败:', error);\n      setResult({\n        type: 'error',\n        title: '获取邀请信息失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理邀请接受\n  const handleAcceptInvitation = async () => {\n    if (!token) return;\n\n    setProcessing(true);\n    try {\n      const response = await InvitationService.acceptInvitationByLink(token, {});\n\n      if (response.success) {\n        // 如果是新用户且返回了访问令牌，自动登录\n        if (response.isNewUser && response.accessToken) {\n          try {\n            TokenManager.setToken(response.accessToken);\n            console.log('新用户自动登录成功');\n\n            setResult({\n              type: 'success',\n              title: '欢迎加入团队！',\n              message: `您的账号已自动创建并登录。${response.nextAction || '正在跳转到仪表盘...'}`,\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: true,\n            });\n\n            // 延迟跳转到仪表盘\n            setTimeout(() => {\n              history.push('/dashboard');\n            }, 3000);\n          } catch (error) {\n            console.error('自动登录失败:', error);\n            setResult({\n              type: 'success',\n              title: '账号创建成功！',\n              message: '您的账号已成功创建，请使用邮箱验证码登录后查看团队信息。',\n              teamName: response.teamName,\n              isNewUser: response.isNewUser,\n              autoLogin: false,\n            });\n          }\n        } else if (response.isNewUser) {\n          // 新用户但没有自动登录令牌\n          setResult({\n            type: 'success',\n            title: '账号创建成功！',\n            message: '您的账号已成功创建，请使用邮箱验证码登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        } else {\n          // 现有用户\n          setResult({\n            type: 'success',\n            title: '加入成功！',\n            message: response.nextAction || '您已成功加入团队，请登录后查看团队信息。',\n            teamName: response.teamName,\n            isNewUser: response.isNewUser,\n            autoLogin: false,\n          });\n        }\n      } else {\n        setResult({\n          type: 'error',\n          title: '加入失败',\n          message: response.errorMessage || '处理邀请时发生错误',\n        });\n      }\n    } catch (error) {\n      console.error('处理邀请失败:', error);\n      setResult({\n        type: 'error',\n        title: '加入失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    history.push('/');\n  };\n\n  // 邀请确认界面\n  const InvitationConfirm = () => {\n    if (!invitationInfo) return null;\n\n    return (\n      <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n        <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />\n        <Title level={3}>团队邀请</Title>\n\n        <div style={{ marginBottom: 32 }}>\n          <Space direction=\"vertical\" size=\"middle\" style={{ width: '100%' }}>\n            <div>\n              <Text type=\"secondary\">您被邀请加入团队：</Text>\n              <br />\n              <Title level={4} style={{ margin: '8px 0', color: '#1890ff' }}>\n                {invitationInfo.teamName}\n              </Title>\n            </div>\n\n            {invitationInfo.inviterName && (\n              <div>\n                <Text type=\"secondary\">邀请人：</Text>\n                <Text strong>{invitationInfo.inviterName}</Text>\n              </div>\n            )}\n\n            {invitationInfo.message && (\n              <div>\n                <Text type=\"secondary\">邀请消息：</Text>\n                <br />\n                <Text italic>\"{invitationInfo.message}\"</Text>\n              </div>\n            )}\n          </Space>\n        </div>\n\n        <Divider />\n\n        <div style={{ marginTop: 24 }}>\n          <Title level={4} style={{ marginBottom: 24 }}>\n            您确定要加入此团队吗？\n          </Title>\n\n          <Space size=\"large\">\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              loading={processing}\n              onClick={handleAcceptInvitation}\n              icon={<CheckCircleOutlined />}\n              disabled={invitationInfo.isExpired}\n            >\n              {processing ? '正在处理...' : '确认加入'}\n            </Button>\n            <Button\n              size=\"large\"\n              onClick={handleCancel}\n              icon={<CloseCircleOutlined />}\n              disabled={processing}\n            >\n              取消\n            </Button>\n          </Space>\n        </div>\n\n        {invitationInfo.isExpired && (\n          <Alert\n            message=\"邀请已过期\"\n            description=\"此邀请链接已过期，请联系团队管理员重新发送邀请。\"\n            type=\"warning\"\n            showIcon\n            style={{ marginTop: 24 }}\n          />\n        )}\n      </div>\n    );\n  };\n\n  // 结果展示\n  const ResultDisplay = () => {\n    if (!result) return null;\n\n    // 根据结果类型和自动登录状态显示不同的按钮\n    const getExtraButtons = () => {\n      if (result.type === 'success') {\n        if (result.autoLogin) {\n          // 自动登录成功，显示跳转到仪表盘的按钮\n          return [\n            <Button type=\"primary\" key=\"dashboard\" onClick={() => history.push('/dashboard')}>\n              前往仪表盘\n            </Button>,\n          ];\n        } else if (result.isNewUser) {\n          // 新用户但未自动登录，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        } else {\n          // 现有用户，引导去登录\n          return [\n            <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n              前往登录\n            </Button>,\n            <Button key=\"home\" onClick={() => history.push('/')}>\n              返回首页\n            </Button>,\n          ];\n        }\n      } else {\n        // 错误情况，显示重试和返回首页\n        return [\n          <Button type=\"primary\" key=\"retry\" onClick={() => window.location.reload()}>\n            重试\n          </Button>,\n          <Button key=\"home\" onClick={() => history.push('/')}>\n            返回首页\n          </Button>,\n        ];\n      }\n    };\n\n    return (\n      <Result\n        status={result.type}\n        title={result.title}\n        subTitle={result.message}\n        extra={getExtraButtons()}\n      />\n    );\n  };\n\n  // 页面加载时获取邀请信息\n  useEffect(() => {\n    fetchInvitationInfo();\n  }, [token]);\n\n  // 加载中状态\n  if (loading) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <div style={{ textAlign: 'center', padding: '60px 20px' }}>\n              <Spin size=\"large\" />\n              <div style={{ marginTop: 16 }}>\n                <Text type=\"secondary\">正在加载邀请信息...</Text>\n              </div>\n            </div>\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示结果页面\n  if (result) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <ResultDisplay />\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  // 显示邀请确认页面\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          团队邀请\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队邀请</Title>\n              <Text type=\"secondary\">加入团队，开始协作</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.inviteCard}>\n          <InvitationConfirm />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default InvitePage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,oBACA;IACE,SAAS;;;;;;wCC8Zb;;;2BAAA;;;;;;;0CA5ZuE;wCAC5B;yCAUpC;8CACsB;oFACc;+CACpB;6CACW;;;;;;6FAGb;;;;;;;;;;YAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;YAE7C,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;gBACvC,OAAO;oBACL,WAAW;wBACT,SAAS;wBACT,eAAe;wBACf,QAAQ;wBACR,UAAU;wBACV,iBACE;wBACF,gBAAgB;oBAClB;oBACA,SAAS;wBACP,MAAM;wBACN,SAAS;wBACT,eAAe;wBACf,gBAAgB;wBAChB,YAAY;wBACZ,SAAS;oBACX;oBACA,QAAQ;wBACN,cAAc;wBACd,WAAW;oBACb;oBACA,MAAM;wBACJ,cAAc;oBAChB;oBACA,OAAO;wBACL,cAAc;oBAChB;oBACA,YAAY;wBACV,OAAO;wBACP,UAAU;wBACV,WAAW,MAAM,iBAAiB;oBACpC;oBACA,QAAQ;wBACN,WAAW;wBACX,WAAW;oBACb;gBACF;YACF;YAEA,MAAM,aAAuB;;gBAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,cAAS;gBAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,eAAQ,EAAgC;gBACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAM;gBAC1C,MAAM,EAAE,MAAM,EAAE,GAAG;gBAEnB,SAAS;gBACT,MAAM,sBAAsB;oBAC1B,IAAI,CAAC,OAAO;wBACV,UAAU;4BACR,MAAM;4BACN,OAAO;4BACP,SAAS;wBACX;wBACA,WAAW;wBACX;oBACF;oBAEA,IAAI;wBACF,MAAM,WAAW,MAAM,2BAAiB,CAAC,iBAAiB,CAAC;wBAE3D,IAAI,SAAS,OAAO,EAClB,kBAAkB;6BAElB,UAAU;4BACR,MAAM;4BACN,OAAO;4BACP,SAAS,SAAS,YAAY,IAAI;wBACpC;oBAEJ,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,aAAa;wBAC3B,UAAU;4BACR,MAAM;4BACN,OAAO;4BACP,SAAS;wBACX;oBACF,SAAU;wBACR,WAAW;oBACb;gBACF;gBAEA,SAAS;gBACT,MAAM,yBAAyB;oBAC7B,IAAI,CAAC,OAAO;oBAEZ,cAAc;oBACd,IAAI;wBACF,MAAM,WAAW,MAAM,2BAAiB,CAAC,sBAAsB,CAAC,OAAO,CAAC;wBAExE,IAAI,SAAS,OAAO,EAAE;4BACpB,sBAAsB;4BACtB,IAAI,SAAS,SAAS,IAAI,SAAS,WAAW,EAC5C,IAAI;gCACF,0BAAY,CAAC,QAAQ,CAAC,SAAS,WAAW;gCAC1C,QAAQ,GAAG,CAAC;gCAEZ,UAAU;oCACR,MAAM;oCACN,OAAO;oCACP,SAAS,CAAC,aAAa,EAAE,SAAS,UAAU,IAAI,cAAc,CAAC;oCAC/D,UAAU,SAAS,QAAQ;oCAC3B,WAAW,SAAS,SAAS;oCAC7B,WAAW;gCACb;gCAEA,WAAW;gCACX,WAAW;oCACT,YAAO,CAAC,IAAI,CAAC;gCACf,GAAG;4BACL,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,WAAW;gCACzB,UAAU;oCACR,MAAM;oCACN,OAAO;oCACP,SAAS;oCACT,UAAU,SAAS,QAAQ;oCAC3B,WAAW,SAAS,SAAS;oCAC7B,WAAW;gCACb;4BACF;iCACK,IAAI,SAAS,SAAS,EAC3B,eAAe;4BACf,UAAU;gCACR,MAAM;gCACN,OAAO;gCACP,SAAS;gCACT,UAAU,SAAS,QAAQ;gCAC3B,WAAW,SAAS,SAAS;gCAC7B,WAAW;4BACb;iCAEA,OAAO;4BACP,UAAU;gCACR,MAAM;gCACN,OAAO;gCACP,SAAS,SAAS,UAAU,IAAI;gCAChC,UAAU,SAAS,QAAQ;gCAC3B,WAAW,SAAS,SAAS;gCAC7B,WAAW;4BACb;wBAEJ,OACE,UAAU;4BACR,MAAM;4BACN,OAAO;4BACP,SAAS,SAAS,YAAY,IAAI;wBACpC;oBAEJ,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;wBACzB,UAAU;4BACR,MAAM;4BACN,OAAO;4BACP,SAAS;wBACX;oBACF,SAAU;wBACR,cAAc;oBAChB;gBACF;gBAEA,SAAS;gBACT,MAAM,eAAe;oBACnB,YAAO,CAAC,IAAI,CAAC;gBACf;gBAEA,SAAS;gBACT,MAAM,oBAAoB;oBACxB,IAAI,CAAC,gBAAgB,OAAO;oBAE5B,qBACE,2BAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,SAAS;wBAAY;;0CACtD,2BAAC,mBAAY;gCAAC,OAAO;oCAAE,UAAU;oCAAI,OAAO;oCAAW,cAAc;gCAAG;;;;;;0CACxE,2BAAC;gCAAM,OAAO;0CAAG;;;;;;0CAEjB,2BAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,MAAK;oCAAS,OAAO;wCAAE,OAAO;oCAAO;;sDAC/D,2BAAC;;8DACC,2BAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,2BAAC;;;;;8DACD,2BAAC;oDAAM,OAAO;oDAAG,OAAO;wDAAE,QAAQ;wDAAS,OAAO;oDAAU;8DACzD,eAAe,QAAQ;;;;;;;;;;;;wCAI3B,eAAe,WAAW,kBACzB,2BAAC;;8DACC,2BAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,2BAAC;oDAAK,MAAM;8DAAE,eAAe,WAAW;;;;;;;;;;;;wCAI3C,eAAe,OAAO,kBACrB,2BAAC;;8DACC,2BAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,2BAAC;;;;;8DACD,2BAAC;oDAAK,MAAM;;wDAAC;wDAAE,eAAe,OAAO;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAM9C,2BAAC,aAAO;;;;;0CAER,2BAAC;gCAAI,OAAO;oCAAE,WAAW;gCAAG;;kDAC1B,2BAAC;wCAAM,OAAO;wCAAG,OAAO;4CAAE,cAAc;wCAAG;kDAAG;;;;;;kDAI9C,2BAAC,WAAK;wCAAC,MAAK;;0DACV,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAS;gDACT,SAAS;gDACT,oBAAM,2BAAC,0BAAmB;;;;;gDAC1B,UAAU,eAAe,SAAS;0DAEjC,aAAa,YAAY;;;;;;0DAE5B,2BAAC,YAAM;gDACL,MAAK;gDACL,SAAS;gDACT,oBAAM,2BAAC,0BAAmB;;;;;gDAC1B,UAAU;0DACX;;;;;;;;;;;;;;;;;;4BAMJ,eAAe,SAAS,kBACvB,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAY;gCACZ,MAAK;gCACL,QAAQ;gCACR,OAAO;oCAAE,WAAW;gCAAG;;;;;;;;;;;;gBAKjC;gBAEA,OAAO;gBACP,MAAM,gBAAgB;oBACpB,IAAI,CAAC,QAAQ,OAAO;oBAEpB,uBAAuB;oBACvB,MAAM,kBAAkB;wBACtB,IAAI,OAAO,IAAI,KAAK,WAAW;4BAC7B,IAAI,OAAO,SAAS,EAClB,qBAAqB;4BACrB,OAAO;8CACL,2BAAC,YAAM;oCAAC,MAAK;oCAA0B,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8CAAe;mCAAvD;;;;;6BAG5B;iCACI,IAAI,OAAO,SAAS,EACzB,kBAAkB;4BAClB,OAAO;8CACL,2BAAC,YAAM;oCAAC,MAAK;oCAAsB,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8CAAgB;mCAApD;;;;;8CAG3B,2BAAC,YAAM;oCAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8CAAM;mCAAzC;;;;;6BAGb;iCAED,aAAa;4BACb,OAAO;8CACL,2BAAC,YAAM;oCAAC,MAAK;oCAAsB,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8CAAgB;mCAApD;;;;;8CAG3B,2BAAC,YAAM;oCAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8CAAM;mCAAzC;;;;;6BAGb;wBAEL,OACE,iBAAiB;wBACjB,OAAO;0CACL,2BAAC,YAAM;gCAAC,MAAK;gCAAsB,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;0CAAI;+BAAjD;;;;;0CAG3B,2BAAC,YAAM;gCAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0CAAM;+BAAzC;;;;;yBAGb;oBAEL;oBAEA,qBACE,2BAAC,YAAM;wBACL,QAAQ,OAAO,IAAI;wBACnB,OAAO,OAAO,KAAK;wBACnB,UAAU,OAAO,OAAO;wBACxB,OAAO;;;;;;gBAGb;gBAEA,cAAc;gBACd,IAAA,gBAAS,EAAC;oBACR;gBACF,GAAG;oBAAC;iBAAM;gBAEV,QAAQ;gBACR,IAAI,SACF,qBACE,2BAAC;oBAAI,WAAW,OAAO,SAAS;;sCAC9B,2BAAC,WAAM;sCACL,cAAA,2BAAC;;oCAAM;oCAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;sCAG7C,2BAAC;4BAAI,WAAW,OAAO,OAAO;sCAC5B,cAAA,2BAAC,UAAI;gCAAC,WAAW,OAAO,UAAU;0CAChC,cAAA,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;oCAAY;;sDACtD,2BAAC,UAAI;4CAAC,MAAK;;;;;;sDACX,2BAAC;4CAAI,OAAO;gDAAE,WAAW;4CAAG;sDAC1B,cAAA,2BAAC;gDAAK,MAAK;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK/B,2BAAC,kBAAM;;;;;;;;;;;gBAKb,SAAS;gBACT,IAAI,QACF,qBACE,2BAAC;oBAAI,WAAW,OAAO,SAAS;;sCAC9B,2BAAC,WAAM;sCACL,cAAA,2BAAC;;oCAAM;oCAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;sCAG7C,2BAAC;4BAAI,WAAW,OAAO,OAAO;sCAC5B,cAAA,2BAAC,UAAI;gCAAC,WAAW,OAAO,UAAU;0CAChC,cAAA,2BAAC;;;;;;;;;;;;;;;sCAGL,2BAAC,kBAAM;;;;;;;;;;;gBAKb,WAAW;gBACX,qBACE,2BAAC;oBAAI,WAAW,OAAO,SAAS;;sCAC9B,2BAAC,WAAM;sCACL,cAAA,2BAAC;;oCAAM;oCAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;sCAG7C,2BAAC;4BAAI,WAAW,OAAO,OAAO;;8CAC5B,2BAAC;oCAAI,WAAW,OAAO,MAAM;8CAC3B,cAAA,2BAAC,WAAK;wCAAC,WAAU;wCAAW,OAAM;wCAAS,MAAK;;0DAC9C,2BAAC;gDAAI,WAAW,OAAO,IAAI;0DACzB,cAAA,2BAAC;oDAAI,KAAI;oDAAY,KAAI;oDAAW,QAAQ;;;;;;;;;;;0DAE9C,2BAAC;gDAAI,WAAW,OAAO,KAAK;;kEAC1B,2BAAC;wDAAM,OAAO;kEAAG;;;;;;kEACjB,2BAAC;wDAAK,MAAK;kEAAY;;;;;;;;;;;;;;;;;;;;;;;8CAK7B,2BAAC,UAAI;oCAAC,WAAW,OAAO,UAAU;8CAChC,cAAA,2BAAC;;;;;;;;;;8CAGH,2BAAC;oCAAI,WAAW,OAAO,MAAM;8CAC3B,cAAA,2BAAC;wCAAK,MAAK;kDAAY;;;;;;;;;;;;;;;;;sCAG3B,2BAAC,kBAAM;;;;;;;;;;;YAGb;eA3VM;;oBACc,cAAS;oBAKR;;;iBANf;gBA6VN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID9ZD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;QAAC,wCAAuC;YAAC;YAAS;SAAuC;IAAA;;AACx+B"}